
import React from 'react';
import { cn } from '../../lib/utils';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'ghost' | 'link' | 'gradient' | 'success' | 'warning';
  size?: 'xs' | 'sm' | 'default' | 'lg' | 'xl' | 'icon';
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', loading = false, leftIcon, rightIcon, children, disabled, ...props }, ref) => {
    const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 ease-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-brand-500 dark:focus-visible:ring-offset-neutral-900 disabled:opacity-50 disabled:pointer-events-none relative overflow-hidden';

    const variants = {
      default: 'bg-gradient-to-r from-brand-600 to-brand-700 text-white shadow-soft hover:shadow-medium hover:from-brand-700 hover:to-brand-800 active:scale-[0.98]',
      secondary: 'bg-neutral-100 dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 shadow-soft hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:shadow-medium active:scale-[0.98]',
      destructive: 'bg-gradient-to-r from-error-600 to-error-700 text-white shadow-soft hover:shadow-medium hover:from-error-700 hover:to-error-800 active:scale-[0.98]',
      outline: 'border-2 border-neutral-300 dark:border-neutral-700 bg-transparent text-neutral-900 dark:text-neutral-100 hover:bg-neutral-50 dark:hover:bg-neutral-800 hover:border-neutral-400 dark:hover:border-neutral-600 active:scale-[0.98]',
      ghost: 'text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800 hover:text-neutral-900 dark:hover:text-neutral-100 active:scale-[0.98]',
      link: 'text-brand-600 dark:text-brand-400 underline-offset-4 hover:underline hover:text-brand-700 dark:hover:text-brand-300',
      gradient: 'bg-gradient-to-r from-brand-500 via-purple-500 to-accent-500 text-white shadow-soft hover:shadow-glow hover:scale-[1.02] active:scale-[0.98]',
      success: 'bg-gradient-to-r from-success-600 to-success-700 text-white shadow-soft hover:shadow-medium hover:from-success-700 hover:to-success-800 active:scale-[0.98]',
      warning: 'bg-gradient-to-r from-warning-500 to-warning-600 text-white shadow-soft hover:shadow-medium hover:from-warning-600 hover:to-warning-700 active:scale-[0.98]',
    };

    const sizes = {
      xs: 'h-7 px-2 text-xs rounded-lg gap-1',
      sm: 'h-8 px-3 text-sm rounded-lg gap-1.5',
      default: 'h-10 px-4 text-sm rounded-xl gap-2',
      lg: 'h-12 px-6 text-base rounded-xl gap-2',
      xl: 'h-14 px-8 text-lg rounded-2xl gap-3',
      icon: 'h-10 w-10 rounded-xl',
    };

    const isDisabled = disabled || loading;

    return (
      <button
        className={cn(baseClasses, variants[variant], sizes[size], className)}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-inherit">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          </div>
        )}
        <div className={cn('flex items-center gap-inherit', loading && 'opacity-0')}>
          {leftIcon && <span className="flex-shrink-0">{leftIcon}</span>}
          {children}
          {rightIcon && <span className="flex-shrink-0">{rightIcon}</span>}
        </div>
      </button>
    );
  }
);
Button.displayName = 'Button';

export { Button };
