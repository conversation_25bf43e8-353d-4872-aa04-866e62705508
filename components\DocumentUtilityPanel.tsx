import React from 'react';
import { Document as DocType, User, DashboardView, ActivityLog, Obligation } from '../types';
import { Button } from './ui/Button';
import { CloseIcon, WandIcon, ClockIcon, MessageSquarePlusIcon, ShieldCheckIcon, SignatureIcon, ActivityIcon, UsersIcon, LockSolidIcon, CheckCircleIcon } from './Icons';
import ClauseSuggestionsPanel from './ClauseSuggestionsPanel';
import VersionHistoryPanel from './VersionHistoryPanel';
import CommentsPanel from './CommentsPanel';
import AnalysisPanel from './AnalysisPanel';
import SignaturePanel from './SignaturePanel';
import { cn } from '../lib/utils';
import ApprovalsPanel from './ApprovalsPanel';
import ObligationsPanel from './ObligationsPanel';

type UtilityTab = 'suggestions' | 'history' | 'comments' | 'analysis' | 'signatures' | 'activity' | 'approvals' | 'obligations';

interface DocumentUtilityPanelProps {
  document: DocType;
  isEditing: boolean;
  onInsertClause: (clauseContent: string) => void;
  onRevert: (documentId: string, versionId: string) => void;
  user: User;
  allUsers: User[];
  setView: (view: DashboardView) => void;
  documentContent: string;
  activeTool: UtilityTab | null;
  setActiveTool: (tab: UtilityTab | null) => void;
  onAddReply: (documentId: string, threadId: string, content: string) => void;
  onResolveThread: (documentId: string, threadId: string) => void;
  onDeleteComment: (documentId: string, threadId: string, commentId: string) => void;
  activityLogs: ActivityLog[];
  isOpen: boolean;
  onClose: () => void;
  onUpdateObligationStatus: (docId: string, obligationId: string, status: Obligation['status']) => void;
}

const tabConfig: { id: UtilityTab; icon: React.FC<{className?: string}>; label: string; premium?: boolean; }[] = [
    { id: 'activity', icon: ActivityIcon, label: 'Activity' },
    { id: 'comments', icon: MessageSquarePlusIcon, label: 'Comments' },
    { id: 'history', icon: ClockIcon, label: 'Versions' },
    { id: 'signatures', icon: SignatureIcon, label: 'Signatures' },
    { id: 'approvals', icon: UsersIcon, label: 'Approvals', premium: true },
    { id: 'obligations', icon: CheckCircleIcon, label: 'Obligations', premium: true },
    { id: 'suggestions', icon: WandIcon, label: 'AI Suggestions', premium: true },
    { id: 'analysis', icon: ShieldCheckIcon, label: 'AI Analysis', premium: true },
];

const ActivityPanel: React.FC<{ logs: ActivityLog[], allUsers: User[] }> = ({ logs, allUsers }) => {
    const getUser = (email: string) => allUsers.find(u => u.email === email);
    
    return (
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {logs.length > 0 ? logs.map(log => {
                const user = getUser(log.userEmail);
                return (
                    <div key={log.id} className="flex items-start gap-3">
                        <img src={user?.avatarUrl} alt={user?.name || log.userEmail} className="w-8 h-8 rounded-full mt-1"/>
                        <div>
                            <p className="text-sm text-zinc-800 dark:text-zinc-200">
                                <span className="font-semibold">{user?.name || log.userEmail}</span> {log.details}
                            </p>
                            <p className="text-xs text-zinc-500 dark:text-zinc-400">{new Date(log.timestamp).toLocaleString()}</p>
                        </div>
                    </div>
                );
            }) : (
                <p className="text-center text-sm text-zinc-500 dark:text-zinc-400 pt-8">No activity recorded yet.</p>
            )}
        </div>
    );
};

const TooltipIconButton: React.FC<{
  title: string;
  isActive: boolean;
  isDisabled: boolean;
  onClick: () => void;
  children: React.ReactNode;
}> = ({ title, isActive, isDisabled, onClick, children }) => {
  return (
    <div className="relative group flex justify-center">
      <button
        onClick={onClick}
        disabled={isDisabled}
        className={cn(
          'w-12 h-12 rounded-lg flex items-center justify-center relative transition-colors',
          isActive
            ? 'bg-brand-50 text-brand-600 dark:bg-zinc-700 dark:text-brand-400'
            : 'text-zinc-500 hover:bg-zinc-100 hover:text-zinc-700 dark:hover:bg-zinc-800 dark:text-zinc-400 dark:hover:text-zinc-100',
          isDisabled && 'text-zinc-400 dark:text-zinc-600 cursor-not-allowed hover:bg-transparent dark:hover:bg-transparent'
        )}
        aria-label={title}
      >
        {children}
        {isActive && <div className="absolute left-0 top-1/4 h-1/2 w-1 bg-brand-600 dark:bg-brand-400 rounded-r-full"></div>}
        {isDisabled && <LockSolidIcon className="w-3 h-3 absolute bottom-1 right-1 text-amber-500" />}
      </button>
      <div className="absolute right-full top-1/2 -translate-y-1/2 mr-4 w-max px-2 py-1 bg-zinc-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-20">
        {isDisabled ? 'Upgrade to Premium' : title}
      </div>
    </div>
  );
};

const DocumentUtilityPanel: React.FC<DocumentUtilityPanelProps> = ({
  document,
  onInsertClause,
  onRevert,
  user,
  allUsers,
  setView,
  documentContent,
  activeTool,
  setActiveTool,
  onAddReply,
  onResolveThread,
  onDeleteComment,
  activityLogs,
  isOpen,
  onClose,
  onUpdateObligationStatus,
}) => {
    const isPremium = user.planName === 'Premium' || user.planName === 'Enterprise';
    
    const renderTabContent = (tool: UtilityTab) => {
        switch (tool) {
            case 'activity':
                return <ActivityPanel logs={activityLogs} allUsers={allUsers} />;
            case 'suggestions':
                return <ClauseSuggestionsPanel documentContent={documentContent} onInsertClause={onInsertClause} user={user} setView={setView} />;
            case 'history':
                return <VersionHistoryPanel document={document} onRevert={onRevert} />;
            case 'comments':
                return <CommentsPanel document={document} currentUser={user} allUsers={allUsers} onAddReply={onAddReply} onResolveThread={onResolveThread} onDeleteComment={onDeleteComment} />;
            case 'analysis':
                return <AnalysisPanel documentContent={documentContent} />;
            case 'signatures':
                return <SignaturePanel document={document} allUsers={allUsers} />;
            case 'approvals':
                return <ApprovalsPanel document={document} allUsers={allUsers} />;
            case 'obligations':
                return <ObligationsPanel document={document} onUpdateStatus={onUpdateObligationStatus} />;
            default:
                return null;
        }
    };
    
    const activeTabConfig = tabConfig.find(t => t.id === activeTool);

    return (
        <>
            {isOpen && <div onClick={onClose} className="fixed inset-0 bg-black/50 z-20 md:hidden" />}
            <div className={cn(
                "w-[400px] max-w-[90vw] flex-shrink-0 border-l border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900 flex h-full",
                "fixed md:relative inset-y-0 right-0 z-30 transition-transform transform",
                isOpen ? "translate-x-0" : "translate-x-full",
                "md:translate-x-0 md:w-[320px] xl:w-[400px]"
            )}>
                <div className="flex-1 flex flex-col bg-white dark:bg-zinc-950 overflow-hidden">
                    {activeTool ? (
                        <div className="flex-1 flex flex-col h-full">
                            <header className="p-4 border-b border-zinc-200 dark:border-zinc-800 flex items-center justify-between flex-shrink-0">
                                <h3 className="font-semibold text-zinc-800 dark:text-zinc-200 flex items-center gap-2">
                                    {activeTabConfig && <activeTabConfig.icon className="w-5 h-5 text-zinc-500"/>}
                                    {activeTabConfig?.label}
                                </h3>
                                <Button variant="ghost" size="icon" onClick={() => setActiveTool(null)} className="h-8 w-8">
                                    <CloseIcon className="w-5 h-5" />
                                </Button>
                            </header>
                            {renderTabContent(activeTool)}
                        </div>
                    ) : (
                        <div className="flex-1 flex flex-col items-center justify-center text-center p-6">
                            <div className="w-16 h-16 bg-zinc-100 dark:bg-zinc-800 rounded-full flex items-center justify-center mb-4">
                                <WandIcon className="w-8 h-8 text-zinc-400 dark:text-zinc-600"/>
                            </div>
                            <h3 className="font-semibold text-zinc-800 dark:text-zinc-200">Document Tools</h3>
                            <p className="text-sm text-zinc-500 dark:text-zinc-400 mt-1">Select a tool from the sidebar to get started.</p>
                        </div>
                    )}
                </div>

                <div className="w-16 flex-shrink-0 border-l border-zinc-200 dark:border-zinc-800 flex flex-col items-center py-2 space-y-1">
                    {tabConfig.map(tab => {
                        const isDisabled = !!tab.premium && !isPremium;
                        return (
                            <TooltipIconButton
                                key={tab.id}
                                title={tab.label}
                                isActive={activeTool === tab.id}
                                isDisabled={isDisabled}
                                onClick={() => {
                                    if(isDisabled) {return;}
                                    setActiveTool(activeTool === tab.id ? null : tab.id);
                                }}
                            >
                                <tab.icon className="w-6 h-6"/>
                            </TooltipIconButton>
                        );
                    })}
                </div>
            </div>
        </>
    );
};

export default DocumentUtilityPanel;