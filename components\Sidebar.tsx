import React, { useState, useRef } from 'react';
import { DashboardView, User } from '../types';
import {
    LegalIcon, DashboardIcon, HistoryIcon, BillingIcon, SettingsIcon, PlusCircleIcon, 
    CloseIcon, TemplateIcon, RepeatIcon, LibraryIcon, LockSolidIcon, UsersIcon, 
    ShieldCheckIcon, QuestionMarkCircleIcon, CheckCircleIcon, GitBranchIcon, BellIcon, ChevronDownIcon, ChevronsLeftIcon, ChevronsRightIcon,
    BookOpenIcon, CogIcon
} from './Icons';
import { cn } from '../lib/utils';

interface SidebarProps {
  currentView: DashboardView;
  setView: (view: DashboardView) => void;
  isSidebarOpen: boolean;
  setIsSidebarOpen: (isOpen: boolean) => void;
  user: User;
  isCollapsed: boolean;
  setIsCollapsed: (isCollapsed: boolean) => void;
}

type NavSubItem = {
    view: DashboardView;
    label: string;
    icon: React.FC<{ className?: string }>;
    premium?: boolean;
};

type NavMenuGroup = {
    label: string;
    icon: React.FC<{ className?: string }>;
    premium?: boolean;
    subItems: NavSubItem[];
};

type NavItem = NavSubItem | NavMenuGroup;

function isNavMenuGroup(item: NavItem): item is NavMenuGroup {
    return (item as NavMenuGroup).subItems !== undefined;
}

const Sidebar: React.FC<SidebarProps> = ({ currentView, setView, isSidebarOpen, setIsSidebarOpen, user, isCollapsed, setIsCollapsed }) => {
    
    const navStructure: readonly NavItem[] = [
        // Dashboard - Main overview
        { view: 'dashboard', label: 'Dashboard', icon: DashboardIcon },
        
        // Documents - Core document workflow
        {
            label: 'Documents',
            icon: HistoryIcon,
            subItems: [
                { view: 'generate', label: 'Create Document', icon: PlusCircleIcon },
                { view: 'history', label: 'All Documents', icon: HistoryIcon },
                { view: 'clients', label: 'Clients', icon: UsersIcon },
            ],
        },
        
        // Resources - Templates and content library
        {
            label: 'Resources',
            icon: LibraryIcon,
            subItems: [
                { view: 'templates', label: 'Templates', icon: TemplateIcon },
                { view: 'clauseLibrary', label: 'Clause Library', icon: BookOpenIcon, premium: true },
            ],
        },
        
        // Workflows - Advanced automation and intelligence
        {
            label: 'Workflows',
            icon: GitBranchIcon,
            premium: true,
            subItems: [
                { view: 'workflows', label: 'Automation', icon: CogIcon, premium: true },
                { view: 'analysis', label: 'Document Analysis', icon: ShieldCheckIcon, premium: true },
                { view: 'lifecycle', label: 'Lifecycle Management', icon: RepeatIcon, premium: true },
                { view: 'obligations', label: 'Obligations Tracking', icon: CheckCircleIcon, premium: true },
                { view: 'integrations', label: 'Integrations', icon: GitBranchIcon, premium: true },
            ],
        },
        
        // Account - User and team management
        {
            label: 'Account',
            icon: SettingsIcon,
            subItems: [
                { view: 'notifications', label: 'Notifications', icon: BellIcon },
                { view: 'team', label: 'Team Management', icon: UsersIcon, premium: true },
                { view: 'subscription', label: 'Subscription', icon: BillingIcon },
                { view: 'settings', label: 'Settings', icon: SettingsIcon },
            ],
        },
        
        // Help - Support and assistance
        { view: 'help', label: 'Help Center', icon: QuestionMarkCircleIcon },
    ];
    
    const [expandedMenus, setExpandedMenus] = useState<Set<string>>(() => {
        const activeParent = navStructure
            .filter(isNavMenuGroup)
            .find(item => item.subItems?.some(sub => sub.view === currentView));
        return new Set(activeParent ? [activeParent.label] : []);
    });

    const [activePopper, setActivePopper] = useState<string | null>(null);

    const toggleMenu = (label: string) => {
        setExpandedMenus(prev => {
            const newSet = new Set(prev);
            if (newSet.has(label)) {
                newSet.delete(label);
            } else {
                newSet.add(label);
            }
            return newSet;
        });
    };

    const handleNavClick = (view: DashboardView) => {
        setView(view);
        setIsSidebarOpen(false);
    };
    
    const isPremiumUser = user.planName === 'Premium' || user.planName === 'Enterprise';

    const NavLink: React.FC<{ item: NavSubItem; forceShowLabel?: boolean; isSubItem?: boolean; }> = ({ item, forceShowLabel = false, isSubItem = false }) => {
        const [isHovered, setIsHovered] = useState(false);
        // FIX: The `useRef` for the timeout ID was incorrectly typed, leading to potential type conflicts between browser and Node environments. Changed to use `ReturnType<typeof setTimeout>` for robust typing and initialized with null.
        const hideTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
        const id = item.view;

        const handleMouseEnter = () => {
            if (hideTimeout.current) {clearTimeout(hideTimeout.current);}
            setIsHovered(true);
            if(isCollapsed) {setActivePopper(id);}
        };
        const handleMouseLeave = () => {
            hideTimeout.current = setTimeout(() => {
                setIsHovered(false);
                if(isCollapsed) {setActivePopper(prev => prev === id ? null : prev);}
            }, 150);
        };

        const isDisabled = item.premium && !isPremiumUser;
        const isActive = currentView === item.view;
        const showLabel = !isCollapsed || forceShowLabel;
        const isPopperVisible = isCollapsed && isHovered;

        return (
            <div 
                className="relative"
                onMouseEnter={isCollapsed && !forceShowLabel ? handleMouseEnter : undefined}
                onMouseLeave={isCollapsed && !forceShowLabel ? handleMouseLeave : undefined}
            >
                <a
                    href="#"
                    onClick={(e) => { e.preventDefault(); if (!isDisabled) {handleNavClick(item.view);} }}
                    className={cn(
                        'flex items-center py-2.5 text-sm font-medium rounded-md transition-colors',
                        isSubItem ? 'pl-11 pr-3' : 'px-3',
                        isCollapsed && !forceShowLabel && 'lg:justify-center',
                        isActive ? 'bg-brand-50 dark:bg-zinc-800 text-brand-600 dark:text-white' : 
                        isDisabled ? 'text-zinc-400 dark:text-zinc-500 cursor-not-allowed' : 
                        'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 hover:text-zinc-900 dark:hover:text-white'
                    )}
                    aria-disabled={isDisabled}
                    aria-current={isActive ? 'page' : undefined}
                >
                    {!isSubItem && <item.icon className={cn("h-5 w-5 flex-shrink-0", showLabel && 'mr-3')} />}
                    {showLabel && <span className="flex-1">{item.label}</span>}
                    {showLabel && item.premium && !isPremiumUser && (
                        <div className="flex items-center gap-1">
                            <span className="px-1.5 py-0.5 text-xs font-medium bg-gradient-to-r from-amber-400 to-orange-500 text-white rounded-full shadow-sm">
                                PRO
                            </span>
                            <LockSolidIcon className="w-3.5 h-3.5 text-amber-500/80" />
                        </div>
                    )}
                </a>
                
                {isCollapsed && !forceShowLabel && (
                    <div 
                        className={cn(
                            "absolute left-full top-1/2 -translate-y-1/2 ml-4 w-max px-2 py-1 bg-zinc-800 text-white text-xs rounded-md shadow-lg z-50 transition-opacity duration-200",
                            isPopperVisible ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
                        )}
                    >
                        {isDisabled ? `${item.label} (Premium)` : item.label}
                    </div>
                )}
            </div>
        );
    };

    const NavMenuGroupComponent: React.FC<{ item: NavMenuGroup }> = ({ item }) => {
        const [isHovered, setIsHovered] = useState(false);
        // FIX: The `useRef` for the timeout ID was incorrectly typed, leading to potential type conflicts between browser and Node environments. Changed to use `ReturnType<typeof setTimeout>` for robust typing and initialized with null.
        const hideTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
        const id = item.label;

        const handleMouseEnter = () => {
            if (hideTimeout.current) {clearTimeout(hideTimeout.current);}
            setIsHovered(true);
            if(isCollapsed) {setActivePopper(id);}
        };
        const handleMouseLeave = () => {
            hideTimeout.current = setTimeout(() => {
                setIsHovered(false);
                if(isCollapsed) {setActivePopper(prev => prev === id ? null : prev);}
            }, 150);
        };

        const isExpanded = expandedMenus.has(item.label);
        const isCategoryDisabled = item.premium && !isPremiumUser;
        const isPopperVisible = isCollapsed && isHovered;

        return (
             <div 
                className="relative"
                onMouseEnter={isCollapsed ? handleMouseEnter : undefined}
                onMouseLeave={isCollapsed ? handleMouseLeave : undefined}
             >
                <button
                    onClick={() => !isCollapsed && !isCategoryDisabled && toggleMenu(item.label)}
                    className={cn(
                        'w-full flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-md text-left transition-colors',
                        isCollapsed && 'lg:justify-center',
                        isCategoryDisabled 
                            ? 'text-zinc-400 dark:text-zinc-500 cursor-not-allowed'
                            : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800'
                    )}
                    aria-disabled={isCategoryDisabled}
                >
                    <div className={cn("flex items-center", isCollapsed ? 'justify-center' : '')}>
                        <item.icon className={cn("h-5 w-5 flex-shrink-0", !isCollapsed && 'mr-3')} />
                        {!isCollapsed && <span className="flex-1">{item.label}</span>}
                    </div>
                    {!isCollapsed && (
                        <div className="flex items-center gap-1">
                            {isCategoryDisabled && (
                                <div className="flex items-center gap-1">
                                    <span className="px-1.5 py-0.5 text-xs font-medium bg-gradient-to-r from-amber-400 to-orange-500 text-white rounded-full shadow-sm">
                                        PRO
                                    </span>
                                    <LockSolidIcon className="w-3.5 h-3.5 text-amber-500/80" />
                                </div>
                            )}
                            <ChevronDownIcon className={cn('w-4 h-4 transition-transform', isExpanded ? 'rotate-180' : '')} />
                        </div>
                    )}
                </button>

                {isExpanded && !isCollapsed && !isCategoryDisabled && (
                    <div className="pl-4 mt-1 space-y-1">
                        {item.subItems?.map(subItem => <NavLink key={subItem.view} item={subItem} isSubItem={true} />)}
                    </div>
                )}

                {isCollapsed && (
                    <div 
                        className={cn(
                            "absolute left-full top-0 ml-2 w-56 p-2 space-y-1 bg-white dark:bg-zinc-900 rounded-md shadow-lg border border-zinc-200 dark:border-zinc-800 z-50 transition-opacity",
                            isPopperVisible ? "opacity-100 pointer-events-auto" : "opacity-0 pointer-events-none"
                        )}
                    >
                        <div className="px-3 py-2 font-semibold text-sm text-zinc-900 dark:text-zinc-100">{item.label}</div>
                        {isCategoryDisabled ? (
                                <div className="px-3 py-2 text-sm text-zinc-500 dark:text-zinc-400 flex items-center gap-2">
                                    <span className="px-2 py-1 text-xs font-medium bg-gradient-to-r from-amber-400 to-orange-500 text-white rounded-full shadow-sm">
                                        PRO
                                    </span>
                                    <span>Upgrade to Premium</span>
                                </div>
                        ) : (
                            item.subItems?.map(subItem => <NavLink key={subItem.view} item={subItem} forceShowLabel={true} isSubItem={true} />)
                        )}
                    </div>
                )}
            </div>
        );
    }

    return (
        <>
            {isSidebarOpen && <div className="fixed inset-0 bg-black/60 z-30 lg:hidden" onClick={() => setIsSidebarOpen(false)}></div>}
            <aside className={cn(
                'fixed lg:relative flex-shrink-0 bg-white dark:bg-zinc-950 text-zinc-600 dark:text-zinc-300 flex-col h-full z-40 transition-all duration-300 ease-in-out border-r border-zinc-200 dark:border-zinc-800',
                isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0',
                isCollapsed ? 'lg:w-20' : 'lg:w-64',
                'flex'
            )}>
                <div className={cn("flex items-center justify-between h-16 px-4 border-b border-zinc-200 dark:border-zinc-800 flex-shrink-0", isCollapsed && "lg:px-2 lg:justify-center")}>
                    <div className="flex items-center group">
                        <LegalIcon className="h-8 w-8 text-brand-600 dark:text-brand-500" />
                        {!isCollapsed && <span className="ml-3 text-2xl font-bold text-zinc-900 dark:text-white">LexiGen</span>}
                    </div>
                    <button onClick={() => setIsSidebarOpen(false)} className="lg:hidden p-1 text-zinc-500 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-white" aria-label="Close sidebar">
                        <CloseIcon className="w-6 h-6" />
                    </button>
                </div>
                <div className={cn(
                    "flex-1 flex flex-col",
                    activePopper ? 'overflow-visible' : 'overflow-y-auto'
                )}>
                    <nav className="flex-1 px-2 py-4">
                        {navStructure.map((item, index) => {
                            const isLastItem = index === navStructure.length - 1;
                            const nextItem = navStructure[index + 1];
                            const needsSeparator = !isLastItem && 
                                ((item as NavSubItem).view === 'dashboard' || 
                                 (nextItem && isNavMenuGroup(nextItem) && nextItem.label === 'Account'));
                            
                            return (
                                <div key={isNavMenuGroup(item) ? item.label : (item as NavSubItem).view}>
                                    {isNavMenuGroup(item) ? (
                                        <NavMenuGroupComponent item={item} />
                                    ) : (
                                        <NavLink item={item as NavSubItem} />
                                    )}
                                    {needsSeparator && (
                                        <div className="my-4 border-t border-zinc-200 dark:border-zinc-700" />
                                    )}
                                    {!needsSeparator && !isLastItem && <div className="h-2" />}
                                </div>
                            );
                        })}
                    </nav>
                </div>
                 <div className="flex-shrink-0 p-2 border-t border-zinc-200 dark:border-zinc-800 space-y-2">
                    {/* Quota badge for extra visibility */}
                    {(() => {
                        const isPaid = user.planName === 'Premium' || user.planName === 'Enterprise';
                        const quotaTotal = typeof user.quotaTotal === 'number' && isFinite(user.quotaTotal) ? user.quotaTotal : 5;
                        const start = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
                        const end = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1);
                        const fmt = (d: Date) => d.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
                        const period = `${fmt(start)} – ${fmt(end)}`;
                        return (
                          <div className="relative group">
                            <div
                              className={cn(
                                'w-full flex items-center justify-between gap-2 px-3 py-2 rounded-lg text-xs bg-zinc-100 dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300',
                                !isPaid && 'cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-700'
                              )}
                              onClick={() => { if (!isPaid) {handleNavClick('subscription');} }}
                              aria-label={isPaid ? 'Unlimited plan' : 'Monthly quota badge'}
                            >
                              <span className="font-medium">{isPaid ? 'Unlimited' : `MTD: ${user.quotaUsed || 0} / ${quotaTotal}`}</span>
                              {!isPaid && <span className="text-brand-600 font-semibold">Upgrade</span>}
                            </div>
                            <div className="absolute top-full mt-2 left-1/2 -translate-x-1/2 w-max px-2 py-1 bg-zinc-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                              Billing period: {period}
                            </div>
                          </div>
                        );
                    })()}
                    <button
                        onClick={() => handleNavClick('generate')}
                        title="New Document"
                        className={cn(
                        "w-full bg-brand-600 text-white hover:bg-brand-700 flex items-center justify-center rounded-lg text-sm font-semibold shadow transition-all",
                        isCollapsed ? "h-10 w-10 p-0" : "px-4 py-3"
                        )}
                    >
                        <PlusCircleIcon className={cn("w-5 h-5", !isCollapsed && "mr-2")} />
                        {!isCollapsed && <span>New Document</span>}
                    </button>
                    <button
                        onClick={() => setIsCollapsed(!isCollapsed)}
                        className="w-full hidden lg:flex items-center justify-center p-2 rounded-md text-zinc-500 hover:bg-zinc-100 hover:text-zinc-900 dark:hover:bg-zinc-800 dark:hover:text-white"
                        title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
                    >
                        {isCollapsed ? <ChevronsRightIcon className="w-5 h-5" /> : <ChevronsLeftIcon className="w-5 h-5" />}
                    </button>
                </div>
            </aside>
        </>
    );
};

export default Sidebar;
