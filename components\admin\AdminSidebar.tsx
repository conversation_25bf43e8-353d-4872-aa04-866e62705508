
import React, { useState } from 'react';
import { cn } from '../../lib/utils';
import { AdminView } from '../../types';
import { BillingIcon, EditIcon, LegalIcon, ServerIcon, SettingsIcon, UsersIcon } from '../Icons';

interface AdminSidebarProps {
  currentView: AdminView;
  setView: (view: AdminView) => void;
}

const navItems = [
  { view: 'analytics' as const, label: 'Analytics', icon: ServerIcon, description: 'Platform insights & metrics' },
  { view: 'billing' as const, label: 'Customers', icon: UsersIcon, description: 'User management & billing' },
  { view: 'plans' as const, label: 'Plan Management', icon: BillingIcon, description: 'Subscription plans & pricing' },
  { view: 'cms' as const, label: 'CMS', icon: EditIcon, description: 'Content & template management' },
  { view: 'settings' as const, label: 'Platform Settings', icon: SettingsIcon, description: 'System configuration' },
];

const AdminSidebar: React.FC<AdminSidebarProps> = ({ currentView, setView }) => {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  return (
    <aside className="w-72 flex-shrink-0 bg-gradient-to-b from-white via-neutral-50 to-neutral-100 dark:from-neutral-950 dark:via-neutral-900 dark:to-neutral-800 flex flex-col border-r border-neutral-200 dark:border-neutral-800 shadow-soft">
      {/* Header */}
      <div className="flex items-center h-20 px-6 border-b border-neutral-200 dark:border-neutral-800 bg-white/80 dark:bg-neutral-950/80 backdrop-blur-sm">
        <div className="flex items-center group">
          <div className="relative">
            <LegalIcon className="h-10 w-10 text-brand-600 dark:text-brand-500 transition-transform group-hover:scale-110" />
            <div className="absolute -inset-1 bg-gradient-to-r from-brand-500 to-accent-500 rounded-full opacity-0 group-hover:opacity-20 transition-opacity blur-sm" />
          </div>
          <div className="ml-4">
            <span className="text-2xl font-bold bg-gradient-to-r from-neutral-900 via-neutral-700 to-neutral-900 dark:from-neutral-100 dark:via-neutral-300 dark:to-neutral-100 bg-clip-text text-transparent">
              LexiGen
            </span>
            <div className="text-xs font-medium text-brand-600 dark:text-brand-400 tracking-wider uppercase">
              Admin Portal
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
        <div className="mb-6">
          <h3 className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 uppercase tracking-wider mb-3 px-3">
            Platform Management
          </h3>
          <div className="space-y-1">
            {navItems.map(item => {
              const isActive = currentView === item.view;
              const isHovered = hoveredItem === item.view;

              return (
                <div
                  key={item.view}
                  className="relative"
                  onMouseEnter={() => setHoveredItem(item.view)}
                  onMouseLeave={() => setHoveredItem(null)}
                >
                  <a
                    href="#"
                    onClick={(e) => { e.preventDefault(); setView(item.view); }}
                    className={cn(
                      'flex items-center px-4 py-3.5 text-sm font-medium rounded-2xl transition-all duration-200 ease-out group relative overflow-hidden',
                      isActive
                        ? 'bg-gradient-to-r from-brand-500 to-brand-600 text-white shadow-medium'
                        : 'text-neutral-700 dark:text-neutral-300 hover:bg-white/60 dark:hover:bg-neutral-800/60 hover:shadow-soft'
                    )}
                  >
                    {/* Background gradient for active state */}
                    {isActive && (
                      <div className="absolute inset-0 bg-gradient-to-r from-brand-500 to-brand-600 rounded-2xl" />
                    )}

                    {/* Hover effect */}
                    {isHovered && !isActive && (
                      <div className="absolute inset-0 bg-white/60 dark:bg-neutral-800/60 rounded-2xl" />
                    )}

                    {/* Content */}
                    <div className="relative flex items-center w-full">
                      <div className={cn(
                        'flex items-center justify-center w-10 h-10 rounded-xl transition-all duration-200',
                        isActive
                          ? 'bg-white/20 text-white'
                          : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400 group-hover:bg-neutral-200 dark:group-hover:bg-neutral-700'
                      )}>
                        <item.icon className="w-5 h-5" />
                      </div>
                      <div className="ml-3 flex-1">
                        <div className={cn(
                          'font-medium transition-colors',
                          isActive ? 'text-white' : 'text-neutral-900 dark:text-neutral-100'
                        )}>
                          {item.label}
                        </div>
                        <div className={cn(
                          'text-xs transition-colors mt-0.5',
                          isActive ? 'text-white/80' : 'text-neutral-500 dark:text-neutral-400'
                        )}>
                          {item.description}
                        </div>
                      </div>
                    </div>
                  </a>
                </div>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-neutral-200 dark:border-neutral-800 bg-white/50 dark:bg-neutral-950/50 backdrop-blur-sm">
        <div className="text-xs text-neutral-500 dark:text-neutral-400 text-center">
          <div className="font-medium">LexiGen Admin v2.0</div>
          <div className="mt-1">Powered by modern design</div>
        </div>
      </div>
    </aside>
  );
};

export default AdminSidebar;
