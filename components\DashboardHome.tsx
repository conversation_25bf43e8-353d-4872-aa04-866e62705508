


import React, { useMemo } from 'react';
import { User, DashboardView, Document as DocType, Template } from '../types';
import { Button } from './ui/Button';
import { PlusCircleIcon } from './Icons';
import DashboardAnalytics from './DashboardAnalytics';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/Card';
import ActivityChart from './ActivityChart';
// FIX: Imported the `TableHead` component to be used for table headers.
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/Table';

interface DashboardHomeProps {
  user: User;
  setView: (view: DashboardView) => void;
  onViewDocument: (doc: DocType) => void;
  publicTemplates?: Template[];
}

const UpcomingDeadlinesWidget: React.FC<Pick<DashboardHomeProps, 'user' | 'setView' | 'onViewDocument'>> = ({ user, setView, onViewDocument }) => {
    const upcomingObligations = useMemo(() => {
        const now = new Date();
        return user.documents
            .flatMap(doc => (doc.obligations || []).map(ob => ({ ...ob, docName: doc.name, documentId: doc.id })))
            .filter(ob => ob.status === 'pending' && new Date(ob.dueDate) >= now)
            .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
            .slice(0, 5);
    }, [user.documents]);

    return (
        <Card className="lg:col-span-2">
            <CardHeader>
                <CardTitle>Upcoming Deadlines</CardTitle>
                <CardDescription>Key obligations from your active contracts.</CardDescription>
            </CardHeader>
            <CardContent>
                {upcomingObligations.length > 0 ? (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Obligation</TableHead>
                                <TableHead>Document</TableHead>
                                <TableHead className="text-right">Due Date</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {upcomingObligations.map(ob => (
                                <TableRow key={ob.id}>
                                    <TableCell className="max-w-xs truncate">{ob.description}</TableCell>
                                    <TableCell>
                                        <button onClick={() => {
                                            const doc = user.documents.find(d => d.id === ob.documentId);
                                            if (doc) { onViewDocument(doc); }
                                        }} className="font-medium text-brand-600 hover:underline">
                                            {ob.docName}
                                        </button>
                                    </TableCell>
                                    <TableCell className="text-right">{new Date(ob.dueDate).toLocaleDateString()}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                ) : (
                    <p className="text-sm text-zinc-500 text-center py-8">No upcoming deadlines found.</p>
                )}
                 <Button variant="link" className="mt-4" onClick={() => setView('obligations')}>View All Obligations</Button>
            </CardContent>
        </Card>
    );
}

const DashboardHome: React.FC<DashboardHomeProps> = ({ user, setView, onViewDocument, publicTemplates }) => {
    return (
        <div className="p-4 sm:p-6 lg:p-8 space-y-8">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">Welcome back, {user.name || user.email.split('@')[0]}!</h1>
                    <p className="text-zinc-600 dark:text-zinc-400 mt-1">Here's a summary of your account activity.</p>
                </div>
                <Button onClick={() => setView('generate')}>
                    <PlusCircleIcon className="w-5 h-5 mr-2"/>
                    Generate New Document
                </Button>
            </div>
            
            <DashboardAnalytics user={user} setView={setView} publicTemplates={publicTemplates} />

            <div className="grid gap-8 lg:grid-cols-3">
                <Card className="lg:col-span-1">
                    <CardHeader>
                        <CardTitle>Recent Activity</CardTitle>
                        <CardDescription>Documents created in the last 7 days.</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ActivityChart documents={user.documents} />
                    </CardContent>
                </Card>
                <UpcomingDeadlinesWidget user={user} setView={setView} onViewDocument={onViewDocument} />
            </div>
        </div>
    );
};

export default DashboardHome;
