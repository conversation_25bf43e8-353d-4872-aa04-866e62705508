import React, { useState, useEffect } from 'react';
import { LegalIcon, MenuIcon, CloseIcon, ChevronDownIcon, DocumentIcon } from './Icons';
import { User } from '../types';
import Dropdown from './ui/Dropdown';

interface HeaderProps {
  setView: (view: 'home' | 'auth' | 'dashboard') => void;
  currentUser: User | null;
  onLogout: () => void;
}

const navItems = [
  { text: 'Solutions', id: 'solutions', subItems: [
    { href: '#solutions-legal', text: 'For Legal Teams' },
    { href: '#solutions-sales', text: 'For Sales Teams' },
    { href: '#solutions-procurement', text: 'For Procurement' },
  ]},
  { href: '#how-it-works', text: 'How It Works' },
  { href: '#pricing', text: 'Pricing' },
  { href: '#faq', text: 'FAQ' },
];

const Header: React.FC<HeaderProps> = ({ setView, currentUser, onLogout }) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  const handleNavClick = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
    e.preventDefault();
    
    const navigateAndScroll = () => {
      setTimeout(() => {
          const element = document.getElementById(targetId);
          element?.scrollIntoView({ behavior: 'smooth' });
      }, 50);
    }
    
    if (currentUser && window.location.hash) {
      // If logged in and on a different page (e.g. #terms), go to home first
      setView('home');
      navigateAndScroll();
    } else {
      navigateAndScroll();
    }
  }

  return (
    <header className={`sticky top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled || currentUser ? 'bg-white/80 dark:bg-zinc-900/80 backdrop-blur-md shadow-sm border-b border-zinc-200 dark:border-zinc-800' : 'bg-transparent'}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <button onClick={() => setView(currentUser ? 'dashboard' : 'home')} className="flex-shrink-0 flex items-center group cursor-pointer">
            <LegalIcon className="h-8 w-8 text-brand-600" />
            <span className="ml-3 text-2xl font-bold text-zinc-900 dark:text-white">LexiGen</span>
          </button>
          
          {currentUser ? (
            <div className="flex items-center space-x-2 sm:space-x-4">
              <Dropdown>
                <Dropdown.Trigger>
                  <div className="flex items-center gap-3 cursor-pointer">
                    <div className="h-9 w-9 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center text-zinc-700 dark:text-zinc-200 font-semibold">
                      {currentUser.avatarUrl ? (
                        <img src={currentUser.avatarUrl} alt="User avatar" className="h-9 w-9 rounded-full object-cover" />
                      ) : (
                        <span>{(currentUser.name || currentUser.email || 'U').slice(0,1).toUpperCase()}</span>
                      )}
                    </div>
                    <div className="hidden sm:flex flex-col items-start">
                      <span className="text-sm font-medium text-zinc-900 dark:text-white">{currentUser.name || currentUser.email}</span>
                      <span className="text-xs text-zinc-500 dark:text-zinc-400">{currentUser.planName}</span>
                    </div>
                  </div>
                </Dropdown.Trigger>
                <Dropdown.Content align="right">
                  <div className="px-4 py-3">
                    <p className="text-sm font-medium text-zinc-900 dark:text-white truncate">{currentUser.name || 'Account'}</p>
                    <div className="mt-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-zinc-100 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200">
                      {currentUser.planName}
                    </div>
                  </div>
                  <Dropdown.Separator />
                  <Dropdown.Item onClick={() => setView('dashboard')} icon={<DocumentIcon className="w-4 h-4"/>}>Dashboard</Dropdown.Item>
                  <Dropdown.Item onClick={onLogout} className="text-red-600 dark:text-red-400" icon={<CloseIcon className="w-4 h-4"/>}>Log Out</Dropdown.Item>
                </Dropdown.Content>
              </Dropdown>
            </div>
          ) : (
            <>
              <nav className="hidden md:flex md:items-center md:space-x-1">
                {navItems.map(item => (
                    item.subItems ? (
                      <React.Fragment key={item.text}>
                      <Dropdown>
                        <Dropdown.Trigger>
                          <div className="text-zinc-600 dark:text-zinc-300 hover:text-brand-600 dark:hover:text-brand-400 transition-colors px-3 py-2 rounded-md flex items-center gap-1">
                            {item.text}
                            <ChevronDownIcon className="w-4 h-4" />
                          </div>
                        </Dropdown.Trigger>
                        <Dropdown.Content>
                          {item.subItems.map(subItem => (
                            <Dropdown.Item key={subItem.href} onClick={(e) => handleNavClick(e as React.MouseEvent<HTMLAnchorElement>, subItem.href.slice(1))}>
                              {subItem.text}
                            </Dropdown.Item>
                          ))}
                        </Dropdown.Content>
                      </Dropdown>
                      </React.Fragment>
                    ) : (
                      <a key={item.href} href={item.href} onClick={(e) => handleNavClick(e, item.href?.slice(1) || '')} className="text-zinc-600 dark:text-zinc-300 hover:text-brand-600 dark:hover:text-brand-400 transition-colors px-3 py-2 rounded-md">{item.text}</a>
                    )
                  ))}
              </nav>
              <div className="hidden md:flex items-center space-x-4">
                <button onClick={() => setView('auth')} className="text-zinc-600 dark:text-zinc-300 hover:text-brand-600 dark:hover:text-brand-400 transition-colors font-medium">Log In</button>
                <button onClick={() => setView('auth')} className="px-5 py-2.5 text-white font-medium bg-brand-600 hover:bg-brand-700 rounded-lg shadow-sm">
                  Sign Up
                </button>
              </div>
            </>
          )}

          <div className="md:hidden">
            <button onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)} className="p-2 rounded-md text-zinc-600 dark:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-800">
              {isMobileMenuOpen ? <CloseIcon className="w-6 h-6"/> : <MenuIcon className="w-6 h-6"/>}
            </button>
          </div>
        </div>
      </div>
       {/* Mobile Menu */}
      {!currentUser && isMobileMenuOpen && (
        <div className="md:hidden bg-white/95 dark:bg-zinc-900/95 backdrop-blur-md border-t border-zinc-200 dark:border-zinc-800">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
             {navItems.map(item => (
                item.subItems ? (
                    <div key={item.text}>
                        <h3 className="px-3 py-2 text-sm font-semibold text-zinc-500 dark:text-zinc-400 uppercase tracking-wider">{item.text}</h3>
                        <div className="pl-3">
                            {item.subItems.map(subItem => (
                                <a 
                                key={subItem.href} 
                                href={subItem.href} 
                                onClick={(e) => { handleNavClick(e, subItem.href.slice(1)); setIsMobileMenuOpen(false); }} 
                                className="block px-3 py-2 rounded-md text-base font-medium text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-800"
                                >
                                {subItem.text}
                                </a>
                            ))}
                        </div>
                    </div>
                ) : (
                    <a 
                        key={item.href} 
                        href={item.href} 
                        onClick={(e) => { handleNavClick(e, item.href?.slice(1) || ''); setIsMobileMenuOpen(false); }} 
                        className="block px-3 py-2 rounded-md text-base font-medium text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-800"
                    >
                        {item.text}
                    </a>
                )
            ))}
          </div>
          <div className="pt-4 pb-3 border-t border-zinc-200 dark:border-zinc-800">
            <div className="flex items-center justify-center px-5 gap-4">
              <button onClick={() => { setView('auth'); setIsMobileMenuOpen(false); }} className="flex-1 text-center px-5 py-2.5 font-medium rounded-lg text-zinc-600 dark:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-800">Log In</button>
              <button onClick={() => { setView('auth'); setIsMobileMenuOpen(false); }} className="flex-1 text-center px-5 py-2.5 text-white font-medium bg-brand-600 hover:bg-brand-700 rounded-lg shadow-sm">Sign Up</button>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
