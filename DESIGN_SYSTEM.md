# LexiGen Design System v2.0

## Overview
This document outlines the comprehensive design system for LexiGen's UI redesign, focusing on modern aesthetics, improved user experience, and accessibility.

## Design Principles

### 1. **Clarity & Simplicity**
- Clean, uncluttered interfaces with purposeful white space
- Clear visual hierarchy using typography and color
- Intuitive navigation patterns

### 2. **Modern Aesthetics**
- Subtle gradients and modern color palettes
- Soft shadows and rounded corners
- Micro-interactions and smooth animations

### 3. **Accessibility First**
- WCAG 2.1 AA compliance
- High contrast ratios
- Keyboard navigation support
- Screen reader compatibility

### 4. **Consistency**
- Unified design language across admin and user portals
- Consistent spacing, typography, and color usage
- Reusable component patterns

## Color System

### Primary Colors
- **Brand**: Indigo-based palette (#6366f1) for primary actions and branding
- **Accent**: Purple-based palette (#d946ef) for secondary actions and highlights
- **Neutral**: Enhanced gray scale for text and backgrounds

### Semantic Colors
- **Success**: Green palette for positive actions and states
- **Warning**: Amber palette for caution and attention
- **Error**: Red palette for errors and destructive actions

### Gradients
- **Primary Gradient**: `from-brand-500 via-purple-500 to-accent-500`
- **Secondary Gradient**: `from-cyan-500 via-blue-500 to-purple-500`
- **Success Gradient**: `from-emerald-500 via-green-500 to-lime-500`

## Typography

### Font Stack
- **Primary**: Inter (clean, modern sans-serif)
- **Monospace**: JetBrains Mono (for code and technical content)

### Scale
- **Display**: 3xl-6xl for hero text and major headings
- **Headings**: xl-2xl for section headers
- **Body**: base-lg for main content
- **Small**: sm-xs for captions and metadata

## Layout & Spacing

### Grid System
- **Container**: Max-width with responsive breakpoints
- **Columns**: 12-column grid system
- **Gutters**: Consistent 1rem (16px) spacing

### Spacing Scale
- **Micro**: 0.25rem (4px) - 0.5rem (8px)
- **Small**: 0.75rem (12px) - 1rem (16px)
- **Medium**: 1.5rem (24px) - 2rem (32px)
- **Large**: 3rem (48px) - 4rem (64px)
- **XLarge**: 6rem (96px) - 8rem (128px)

## Component Patterns

### Cards
- **Elevation**: Soft shadows with subtle borders
- **Padding**: Consistent internal spacing
- **Radius**: Rounded corners (0.5rem-1rem)
- **Hover States**: Subtle lift and shadow increase

### Buttons
- **Primary**: Brand gradient with white text
- **Secondary**: Outline style with brand color
- **Ghost**: Transparent with hover background
- **Sizes**: sm, base, lg, xl

### Navigation
- **Sidebar**: Collapsible with hover tooltips
- **Breadcrumbs**: Clear path indication
- **Tabs**: Underline style with smooth transitions

### Forms
- **Inputs**: Soft borders with focus states
- **Labels**: Clear hierarchy and spacing
- **Validation**: Inline feedback with semantic colors

## Animation & Interactions

### Micro-interactions
- **Hover**: Subtle scale (1.02x) and shadow changes
- **Focus**: Clear outline with brand color
- **Loading**: Smooth skeleton states and spinners
- **Transitions**: 200-300ms ease-out timing

### Page Transitions
- **Fade In**: 500ms for new content
- **Slide Up**: 300ms for modals and dropdowns
- **Scale In**: 200ms for tooltips and popovers

## Responsive Design

### Breakpoints
- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px
- **Desktop**: 1024px - 1439px
- **Large**: 1440px+

### Mobile-First Approach
- Start with mobile design
- Progressive enhancement for larger screens
- Touch-friendly interactive elements (44px minimum)

## Dark Mode

### Implementation
- CSS custom properties for theme switching
- Proper contrast ratios maintained
- Consistent visual hierarchy in both modes

### Color Adaptations
- **Backgrounds**: Deep neutrals (neutral-900, neutral-950)
- **Text**: Light neutrals (neutral-100, neutral-200)
- **Borders**: Subtle neutral-800 for definition

## Accessibility Guidelines

### Color Contrast
- **Normal Text**: 4.5:1 minimum ratio
- **Large Text**: 3:1 minimum ratio
- **Interactive Elements**: Clear focus indicators

### Keyboard Navigation
- **Tab Order**: Logical flow through interface
- **Focus Management**: Clear visual indicators
- **Shortcuts**: Common keyboard shortcuts supported

### Screen Readers
- **ARIA Labels**: Descriptive labels for all interactive elements
- **Semantic HTML**: Proper heading hierarchy and landmarks
- **Alt Text**: Descriptive text for all images

## Implementation Notes

### CSS Architecture
- **Utility-First**: Tailwind CSS for rapid development
- **Component Classes**: Custom classes for complex components
- **CSS Variables**: For theme switching and customization

### Performance
- **Critical CSS**: Above-the-fold styles inlined
- **Lazy Loading**: Non-critical styles loaded asynchronously
- **Optimization**: Purged unused styles in production

This design system serves as the foundation for the complete UI redesign, ensuring consistency, accessibility, and modern aesthetics across all components.
