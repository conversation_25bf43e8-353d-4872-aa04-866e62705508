import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>laborator, DashboardView, Document as DocType, DocumentsPaginationState, Flow, Notification, NotificationPreferences, Obligation, PricingPlan, SearchResult, Signature, SsoConfig, Team, TeamMemberRole, Template, Theme, User, WorkflowInstance, WorkflowTemplate } from '../types';
import DashboardHeader from './DashboardHeader';
import DashboardPage from './DashboardPage';
import Sidebar from './Sidebar';

interface DashboardLayoutProps {
  user: User;
  team: Team | undefined;
  allUsers: User[]; // Pass all users for collaborator lookups
  allTeams: Team[];
  dataLoading?: boolean;
  dataError?: string | null;
  documentsPagination: DocumentsPaginationState;
  onSaveDocument: (content: string, name: string, folderId: string | null, clientId: string | null, options?: { tags?: string[] }) => void;
  onUpdateDocument: (documentId: string, newContent: string) => void;
  onRevertDocumentVersion: (documentId: string, versionId: string) => void;
  onLogout: () => void;
  onUpdateProfile: (profile: Partial<Pick<User, 'name' | 'username' | 'avatarUrl' | 'jobTitle' | 'company' | 'bio' | 'websiteUrl' | 'linkedinUrl'>>) => void;
  onDeleteAccount: () => void;
  onDeleteDocument: (documentId: string) => void;
  onCreateFolder: (name: string) => void;
  onUpdateFolder: (folderId: string, newName: string) => void;
  onDeleteFolder: (folderId: string) => void;
  onMoveDocument: (documentId: string, folderId: string | null) => void;
  onUpdateCollaborators: (documentId: string, collaborators: Collaborator[]) => void;
  onAddComment: (documentId: string, textSelection: string) => string | undefined;
  onAddReply: (documentId: string, threadId: string, content: string) => void;
  onResolveThread: (documentId: string, threadId: string) => void;
  onDeleteComment: (documentId: string, threadId: string, commentId: string) => void;
  onMarkNotificationRead: (notificationId: string) => void;
  onMarkAllNotificationsRead: () => void;
  onCreateCustomTemplate: (docId: string, templateName: string) => void;
  onDeleteCustomTemplate: (templateId: string) => void;
  onUpgradeSubscription: () => void;
  onUpdateSubscription: (teamId: string, planName: string) => void;
  onRequestSignatures: (docId: string, signers: Omit<Signature, 'id' | 'status' | 'token'>[]) => void;
  onLogDocumentView: (docId: string) => void;
  onUpdateDocumentStatus: (docId: string, newStatus: DocType['status']) => void;
  onCreateClause: (clauseData: Omit<Clause, 'id' | 'createdAt'>) => void;
  onUpdateClause: (clauseId: string, updates: Partial<Omit<Clause, 'id' | 'createdAt'>>) => void;
  onDeleteClause: (clauseId: string) => void;
  onCreateClient: (clientData: Omit<Client, 'id' | 'createdAt'>) => void;
  onUpdateClient: (clientId: string, updates: Partial<Omit<Client, 'id' | 'createdAt'>>) => void;
  onDeleteClient: (clientId: string) => void;
  onUpdateDocumentClient: (documentId: string, clientId: string | null) => void;
  onLoadMoreDocuments: () => void;
  onInviteMember: (email: string, role: TeamMemberRole) => string | null;
  onUpdateMemberRole: (userId: string, role: TeamMemberRole) => void;
  onRemoveMember: (userId: string) => void;
  onUpdateUserSettings: (settings: { theme?: Theme; notificationPreferences?: Partial<NotificationPreferences> }) => void;
  onChangePassword: (oldPassword: string, newPassword: string) => string | null;
  onGenerateApiKey: (name: string) => ApiKey;
  onRevokeApiKey: (keyId: string) => void;
  onUpdateSsoConfig: (config: SsoConfig) => void;
  pricingPlans: PricingPlan[];
  publicTemplates: Template[];
  onRequestApproval: (docId: string, approverEmails: string[]) => void;
  onRespondToApproval: (docId: string, decision: 'approved' | 'changes-requested', comments?: string) => void;
  onUpdateObligationStatus: (docId: string, obligationId: string, status: Obligation['status']) => void;
  onCreateWorkflowTemplate: (templateData: Omit<WorkflowTemplate, 'id' | 'status'>) => void;
  onUpdateWorkflowTemplate: (template: WorkflowTemplate) => void;
  onDeleteWorkflowTemplate: (templateId: string) => void;
  workflowInstances: WorkflowInstance[];
  onCreateConnection: (connectorId: string, credentials: Record<string, string>) => void;
  onDeleteConnection: (connectionId: string) => void;
  onCreateFlow: (flowData: Omit<Flow, 'id' | 'userId' | 'createdAt'>) => void;
  onUpdateFlow: (flowId: string, updates: Partial<Flow>) => void;
  onDeleteFlow: (flowId: string) => void;
  // New: allow parent to navigate into a specific dashboard view (e.g., history) and preselect a folder
  navigate?: { view: DashboardView; selectedFolderId?: string | null; anchorDocId?: string } | null;
  onConsumeNavigate?: () => void;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = (props) => {
  const {
    user,
    team,
    allUsers,
    allTeams,
    dataLoading,
    dataError,
    documentsPagination,
    onSaveDocument,
    onUpdateDocument,
    onRevertDocumentVersion,
    onLogout,
    onUpdateProfile,
    onDeleteAccount,
    onDeleteDocument,
    onCreateFolder,
    onUpdateFolder,
    onDeleteFolder,
    onMoveDocument,
    onUpdateCollaborators,
    onAddComment,
    onAddReply,
    onResolveThread,
    onDeleteComment,
    onMarkNotificationRead,
    onMarkAllNotificationsRead,
    onCreateCustomTemplate,
    onDeleteCustomTemplate,
    onUpgradeSubscription,
    onRequestSignatures,
    onUpdateSubscription,
    onLogDocumentView,
    onUpdateDocumentStatus,
    onCreateClause,
    onUpdateClause,
    onDeleteClause,
    onCreateClient,
    onUpdateClient,
    onDeleteClient,
    onUpdateDocumentClient,
    onLoadMoreDocuments,
    onInviteMember,
    onUpdateMemberRole,
    onRemoveMember,
    onUpdateUserSettings,
    onChangePassword,
    onGenerateApiKey,
    onRevokeApiKey,
    onUpdateSsoConfig,
    pricingPlans,
    publicTemplates,
    onRequestApproval,
    onRespondToApproval,
    onUpdateObligationStatus,
    onCreateWorkflowTemplate,
    onUpdateWorkflowTemplate,
    onDeleteWorkflowTemplate,
    workflowInstances,
    onCreateConnection,
    onDeleteConnection,
    onCreateFlow,
    onUpdateFlow,
    onDeleteFlow,
    navigate,
    onConsumeNavigate,
  } = props;
  const [dashboardView, setDashboardView] = useState<DashboardView>('dashboard'); // Default to dashboard home
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [promptToGenerate, setPromptToGenerate] = useState<string | null>(null);
  const [isTemplateSession, setIsTemplateSession] = useState<boolean>(false);
  const [activeDocument, setActiveDocument] = useState<DocType | null>(null);
  const [activeClient, setActiveClient] = useState<Client | null>(null);
  const [initialUtilityTab, setInitialUtilityTab] = useState<'suggestions' | 'history' | 'comments' | null>(null);
  const [initialHelpTopicId, setInitialHelpTopicId] = useState<string | null>(null);
  const [initialHistoryFolderId, setInitialHistoryFolderId] = useState<string | null | undefined>(undefined);
  const [highlightDocId, setHighlightDocId] = useState<string | undefined>(undefined);

  // Keep activeDocument in sync with latest user.documents
  useEffect(() => {
    if (activeDocument) {
      const fresh = user.documents.find(d => d.id === activeDocument.id);
      if (fresh && fresh !== activeDocument) {
        setActiveDocument(fresh);
      }
    }
  }, [user.documents]);

  // React to navigation intents from the parent (App)
  useEffect(() => {
    if (navigate) {
      if (navigate.view && navigate.view !== dashboardView) {
        setDashboardView(navigate.view);
      }
      if (navigate.view === 'history') {
        setInitialHistoryFolderId(typeof navigate.selectedFolderId === 'undefined' ? undefined : (navigate.selectedFolderId ?? null));
        setHighlightDocId(navigate.anchorDocId);
      }
      // Consumption handled so parent can clear it
      if (onConsumeNavigate) {
        onConsumeNavigate();
      }
    }

  }, [navigate]);

  // Keep activeClient in sync with user updates (reflect edits or deletion)
  useEffect(() => {
    if (activeClient) {
      const next = (user.clients || []).find(c => c.id === activeClient.id) || null;
      if (next) {
        if (next !== activeClient) { setActiveClient(next); }
      } else {
        // Client removed; go back to list
        setActiveClient(null);
        setDashboardView('clients');
      }
    }
  }, [user.clients]);

  useEffect(() => {
    if (activeDocument) {
      const updatedDocFromUser = user.documents.find(doc => doc.id === activeDocument.id);
      if (
        updatedDocFromUser && (
          updatedDocFromUser.updatedAt !== activeDocument.updatedAt ||
          updatedDocFromUser.clientId !== activeDocument.clientId ||
          updatedDocFromUser.name !== activeDocument.name
        )
      ) {
        setActiveDocument(updatedDocFromUser);
      } else if (!updatedDocFromUser) {
        setActiveDocument(null);
        setDashboardView('history');
      }
    }
  }, [user.documents, activeDocument]);

  const handleSelectTemplate = (prompt: string) => {
    setPromptToGenerate(prompt);
    setIsTemplateSession(true);
    setDashboardView('generate');
  };

  const clearPromptToGenerate = () => {
    setPromptToGenerate(null);
  };

  const handleSetView = (view: DashboardView) => {
    if (view !== 'documentDetail') {
      setActiveDocument(null);
    }
    if (view !== 'clientDetail') {
      setActiveClient(null);
    }
    if (view !== 'help') {
      setInitialHelpTopicId(null);
    }
    if (view !== 'generate') {
      setIsTemplateSession(false);
    }
    setDashboardView(view);
  };

  const handleViewDocument = (doc: DocType) => {
    onLogDocumentView(doc.id);
    setActiveDocument(doc);
    setDashboardView('documentDetail');
  };

  const handleViewClient = (client: Client) => {
    setActiveClient(client);
    setDashboardView('clientDetail');
  };

  const handleNotificationClick = (notification: Notification) => {
    onMarkNotificationRead(notification.id);
    let docToView = notification.documentId ? user.documents.find(d => d.id === notification.documentId) : undefined;

    if (!docToView && notification.documentId) {
      for (const u of allUsers) {
        docToView = u.documents.find(d => d.id === notification.documentId && d.collaborators.some(c => c.email === user.email));
        if (docToView) { break; }
      }
    }

    if (docToView) {
      if (notification.type === 'comment') {
        setInitialUtilityTab('comments');
      }
      handleViewDocument(docToView);
    } else if (notification.type === 'team') {
      handleSetView('team');
    } else {
      // Could not find document for notification - handled by UI state
    }
  };

  const handleSearchResultClick = (result: SearchResult) => {
    switch (result.type) {
      case 'document': {
        const doc = user.documents.find(d => d.id === result.id);
        if (doc) {
          handleViewDocument(doc);
        }
        break;
      }
      case 'folder':
        handleSetView('history');
        // Future enhancement: select the specific folder
        break;
      case 'clause':
        handleSetView('clauseLibrary');
        break;
      case 'help':
        setInitialHelpTopicId(result.id);
        handleSetView('help');
        break;
    }
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-neutral-50 via-neutral-100 to-neutral-200 dark:from-neutral-950 dark:via-neutral-900 dark:to-neutral-800">
      <Sidebar
        currentView={dashboardView}
        setView={handleSetView}
        isSidebarOpen={isSidebarOpen}
        setIsSidebarOpen={setIsSidebarOpen}
        user={user}
        isCollapsed={isSidebarCollapsed}
        setIsCollapsed={setIsSidebarCollapsed}
      />
      <div className="flex-1 flex flex-col overflow-hidden">
        <DashboardHeader
          onLogout={onLogout}
          user={user}
          view={dashboardView}
          onToggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)}
          onMarkAllNotificationsRead={onMarkAllNotificationsRead}
          onNotificationClick={handleNotificationClick}
          setView={handleSetView}
          onSearchResultClick={handleSearchResultClick}
          onUpdateUserSettings={(s) => onUpdateUserSettings({ theme: s.theme })}
        />
        <div className="flex-1 overflow-x-hidden overflow-y-auto bg-transparent p-6">
          <div className="max-w-7xl mx-auto">
            <DashboardPage
              user={user}
              team={team}
              allUsers={allUsers}
              allTeams={allTeams}
              dataLoading={dataLoading}
              dataError={dataError}
              documentsPagination={documentsPagination}
              onSaveDocument={onSaveDocument}
              onUpdateDocument={onUpdateDocument}
              onRevertDocumentVersion={onRevertDocumentVersion}
              view={dashboardView}
              setView={handleSetView}
              initialSelectedFolderId={initialHistoryFolderId}
              highlightDocumentId={highlightDocId}
              isTemplateSession={isTemplateSession}
              onUpdateProfile={onUpdateProfile}
              onDeleteAccount={onDeleteAccount}
              onSelectTemplate={handleSelectTemplate}
              promptToGenerate={promptToGenerate}
              onClearPrompt={clearPromptToGenerate}
              onViewDocument={handleViewDocument}
              onViewClient={handleViewClient}
              onDeleteDocument={onDeleteDocument}
              activeDocument={activeDocument}
              activeClient={activeClient}
              onCreateFolder={onCreateFolder}
              onUpdateFolder={onUpdateFolder}
              onDeleteFolder={onDeleteFolder}
              onMoveDocument={onMoveDocument}
              onUpdateCollaborators={onUpdateCollaborators}
              onAddComment={onAddComment}
              onAddReply={onAddReply}
              onResolveThread={onResolveThread}
              onDeleteComment={onDeleteComment}
              initialUtilityTab={initialUtilityTab}
              onClearInitialTab={() => setInitialUtilityTab(null)}
              onCreateCustomTemplate={onCreateCustomTemplate}
              onDeleteCustomTemplate={onDeleteCustomTemplate}
              onUpgradeSubscription={onUpgradeSubscription}
              onRequestSignatures={onRequestSignatures}
              onUpdateSubscription={onUpdateSubscription}
              onLogDocumentView={onLogDocumentView}
              onUpdateDocumentStatus={onUpdateDocumentStatus}
              onNotificationClick={handleNotificationClick}
              onCreateClause={onCreateClause}
              onUpdateClause={onUpdateClause}
              onDeleteClause={onDeleteClause}
              onCreateClient={onCreateClient}
              onUpdateClient={onUpdateClient}
              onDeleteClient={onDeleteClient}
              onUpdateDocumentClient={onUpdateDocumentClient}
              onLoadMoreDocuments={onLoadMoreDocuments}
              onInviteMember={onInviteMember}
              onUpdateMemberRole={onUpdateMemberRole}
              onRemoveMember={onRemoveMember}
              onMarkAllNotificationsRead={onMarkAllNotificationsRead}
              onUpdateUserSettings={onUpdateUserSettings}
              onChangePassword={onChangePassword}
              onGenerateApiKey={onGenerateApiKey}
              onRevokeApiKey={onRevokeApiKey}
              onUpdateSsoConfig={onUpdateSsoConfig}
              initialHelpTopicId={initialHelpTopicId}
              pricingPlans={pricingPlans}
              publicTemplates={publicTemplates}
              onRequestApproval={onRequestApproval}
              onRespondToApproval={onRespondToApproval}
              onUpdateObligationStatus={onUpdateObligationStatus}
              onCreateWorkflowTemplate={onCreateWorkflowTemplate}
              onUpdateWorkflowTemplate={onUpdateWorkflowTemplate}
              onDeleteWorkflowTemplate={onDeleteWorkflowTemplate}
              workflowInstances={workflowInstances}
              onCreateConnection={onCreateConnection}
              onDeleteConnection={onDeleteConnection}
              onCreateFlow={onCreateFlow}
              onUpdateFlow={onUpdateFlow}
              onDeleteFlow={onDeleteFlow}
            />
            <footer className="text-center py-6 px-8 border-t border-neutral-200 dark:border-neutral-800 mt-8 bg-white/50 dark:bg-neutral-950/50 backdrop-blur-sm rounded-2xl">
              <p className="text-sm text-neutral-500 dark:text-neutral-400">
                &copy; {new Date().getFullYear()} LexiGen. All rights reserved. |
                <a href="#terms" className="ml-2 text-brand-600 hover:underline hover:text-brand-700 transition-colors">Terms & Conditions</a>
              </p>
            </footer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
