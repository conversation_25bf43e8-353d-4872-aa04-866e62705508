
import React from 'react';
import { User, AdminView, AdminSearchResult } from '../../types';
import Dropdown from '../ui/Dropdown';
import { SearchIcon, UserIcon, CloseIcon } from '../Icons';
import AdminGlobalSearchResults from './AdminGlobalSearchResults';


interface AdminHeaderProps {
  user: User;
  onLogout: () => void;
  currentView: AdminView;
  setView: (view: AdminView) => void;
  // New search props
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onSearchFocus: () => void;
  isSearchVisible: boolean;
  searchResults: AdminSearchResult[];
  onSearchResultClick: (result: AdminSearchResult) => void;
  searchRef: React.RefObject<HTMLDivElement>;
}

const viewTitles: Record<AdminView, string> = {
    analytics: 'Analytics Dashboard',
    billing: 'Customers',
    plans: 'Plan Management',
    cms: 'Content Management',
    settings: 'Platform Settings',
    profile: 'Your Profile',
};

const AdminHeader: React.FC<AdminHeaderProps> = (props) => {
  const { 
    user, onLogout, currentView, setView,
    searchTerm, onSearchChange, onSearchFocus, isSearchVisible, searchResults, onSearchResultClick, searchRef
  } = props;
  
  return (
    <header className="flex-shrink-0 h-16 bg-white dark:bg-zinc-950 border-b border-zinc-200 dark:border-zinc-800 flex items-center justify-between px-4 sm:px-6 lg:px-8">
      <h1 className="text-xl font-semibold text-zinc-900 dark:text-white">{viewTitles[currentView]}</h1>
      
      <div className="flex-1 flex justify-center px-4">
        <div className="relative w-full max-w-md" ref={searchRef}>
            <div className="relative">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <SearchIcon className="h-5 w-5 text-zinc-400" />
                </div>
                <input 
                    type="text"
                    placeholder="Search users, teams, plans..."
                    value={searchTerm}
                    onChange={(e) => onSearchChange(e.target.value)}
                    onFocus={onSearchFocus}
                    className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-zinc-100 dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-2"
                />
            </div>
            {isSearchVisible && (
                <AdminGlobalSearchResults 
                    results={searchResults}
                    onResultClick={onSearchResultClick}
                    searchTerm={searchTerm}
                />
            )}
        </div>
      </div>

      <Dropdown>
        <Dropdown.Trigger>
          <div className="flex items-center space-x-2 p-1 rounded-full hover:bg-zinc-100 dark:hover:bg-zinc-800 cursor-pointer">
            <img src={user.avatarUrl} alt="User Avatar" className="w-9 h-9 rounded-full" />
            <span className="hidden sm:inline text-sm font-medium text-zinc-700 dark:text-zinc-300">{user.name}</span>
          </div>
        </Dropdown.Trigger>
        <Dropdown.Content align="right">
          <Dropdown.Item onClick={() => setView('profile')} icon={<UserIcon className="w-4 h-4"/>}>Your Profile</Dropdown.Item>
          <Dropdown.Item onClick={onLogout} icon={<CloseIcon className="w-4 h-4"/>}>Log out</Dropdown.Item>
        </Dropdown.Content>
      </Dropdown>
    </header>
  );
};

export default AdminHeader;
