
import React from 'react';
import { AdminSearchResult, AdminView, User } from '../../types';
import { CloseIcon, SearchIcon, UserIcon } from '../Icons';
import Dropdown from '../ui/Dropdown';
import AdminGlobalSearchResults from './AdminGlobalSearchResults';

interface AdminHeaderProps {
  user: User;
  onLogout: () => void;
  currentView: AdminView;
  setView: (view: AdminView) => void;
  // New search props
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onSearchFocus: () => void;
  isSearchVisible: boolean;
  searchResults: AdminSearchResult[];
  onSearchResultClick: (result: AdminSearchResult) => void;
  searchRef: React.RefObject<HTMLDivElement>;
}

const viewTitles: Record<AdminView, string> = {
  analytics: 'Analytics Dashboard',
  billing: 'Customer Management',
  plans: 'Plan Management',
  cms: 'Content Management',
  settings: 'Platform Settings',
  profile: 'Your Profile',
};

const viewDescriptions: Record<AdminView, string> = {
  analytics: 'Monitor platform performance and user engagement',
  billing: 'Manage customers, subscriptions, and billing',
  plans: 'Configure pricing plans and subscription tiers',
  cms: 'Manage templates and platform content',
  settings: 'Configure platform settings and integrations',
  profile: 'Manage your admin account settings',
};

const AdminHeader: React.FC<AdminHeaderProps> = (props) => {
  const {
    user, onLogout, currentView, setView,
    searchTerm, onSearchChange, onSearchFocus, isSearchVisible, searchResults, onSearchResultClick, searchRef
  } = props;

  return (
    <header className="flex-shrink-0 h-20 bg-white/80 dark:bg-neutral-950/80 backdrop-blur-sm border-b border-neutral-200 dark:border-neutral-800 flex items-center justify-between px-6 lg:px-8 shadow-soft">
      {/* Left section - Page title and description */}
      <div className="flex-1">
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-neutral-900 to-neutral-700 dark:from-neutral-100 dark:to-neutral-300 bg-clip-text text-transparent">
              {viewTitles[currentView]}
            </h1>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
              {viewDescriptions[currentView]}
            </p>
          </div>
        </div>
      </div>

      {/* Center section - Search */}
      <div className="flex-1 flex justify-center px-8">
        <div className="relative w-full max-w-lg" ref={searchRef}>
          <input
            type="text"
            placeholder="Search users, teams, plans..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            onFocus={onSearchFocus}
            className="w-full h-12 pl-12 pr-4 bg-neutral-100 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-2xl text-sm placeholder-neutral-500 dark:placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-brand-500/20 focus:border-brand-500 transition-all duration-200"
          />
          <div className="absolute inset-y-0 left-0 flex items-center pl-4">
            <SearchIcon className="h-5 w-5 text-neutral-400" />
          </div>
          {isSearchVisible && (
            <AdminGlobalSearchResults
              results={searchResults}
              onResultClick={onSearchResultClick}
              searchTerm={searchTerm}
            />
          )}
        </div>
      </div>

      {/* Right section - Actions and user menu */}
      <div className="flex items-center gap-3">
        {/* Quick actions */}
        <div className="hidden md:flex items-center gap-2">
          <button
            onClick={() => setView('settings')}
            className="p-2.5 rounded-xl bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400 hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:text-neutral-900 dark:hover:text-neutral-100 transition-all duration-200"
            title="Platform Settings"
          >
            <SettingsIcon className="w-5 h-5" />
          </button>
          <button
            className="p-2.5 rounded-xl bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400 hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:text-neutral-900 dark:hover:text-neutral-100 transition-all duration-200 relative"
            title="Notifications"
          >
            <BellIcon className="w-5 h-5" />
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-error-500 rounded-full border-2 border-white dark:border-neutral-950"></span>
          </button>
        </div>

        {/* User menu */}
        <Dropdown>
          <Dropdown.Trigger>
            <div className="flex items-center gap-3 p-2 rounded-2xl hover:bg-neutral-100 dark:hover:bg-neutral-800 cursor-pointer transition-all duration-200 group">
              <div className="relative">
                <img
                  src={user.avatarUrl}
                  alt="User Avatar"
                  className="w-10 h-10 rounded-xl object-cover ring-2 ring-neutral-200 dark:ring-neutral-700 group-hover:ring-brand-500 transition-all duration-200"
                />
                <div className="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-success-500 rounded-full border-2 border-white dark:border-neutral-950"></div>
              </div>
              <div className="hidden sm:block text-left">
                <div className="text-sm font-semibold text-neutral-900 dark:text-neutral-100">
                  {user.name}
                </div>
                <div className="text-xs text-neutral-500 dark:text-neutral-400">
                  Super Admin
                </div>
              </div>
            </div>
          </Dropdown.Trigger>
          <Dropdown.Content align="right">
            <div className="px-4 py-3 border-b border-neutral-100 dark:border-neutral-800">
              <div className="flex items-center gap-3">
                <img src={user.avatarUrl} alt="User Avatar" className="w-10 h-10 rounded-xl object-cover" />
                <div>
                  <div className="text-sm font-semibold text-neutral-900 dark:text-neutral-100">{user.name}</div>
                  <div className="text-xs text-neutral-500 dark:text-neutral-400">Super Admin</div>
                </div>
              </div>
            </div>
            <Dropdown.Item onClick={() => setView('profile')} icon={<UserIcon className="w-4 h-4" />}>
              Your Profile
            </Dropdown.Item>
            <Dropdown.Item onClick={() => setView('settings')} icon={<SettingsIcon className="w-4 h-4" />}>
              Platform Settings
            </Dropdown.Item>
            <div className="border-t border-neutral-100 dark:border-neutral-800 my-1"></div>
            <Dropdown.Item onClick={onLogout} icon={<CloseIcon className="w-4 h-4" />} className="text-error-600 dark:text-error-400">
              Log out
            </Dropdown.Item>
          </Dropdown.Content>
        </Dropdown>
      </div>
    </header>
  );
};

export default AdminHeader;
