

import React, { useState } from 'react';
import Login from './Login';
import Registration from './Registration';
import ForgotPassword from './ForgotPassword';
import { LegalIcon } from './Icons';

interface AuthPageProps {
  setView: (view: 'home' | 'auth') => void;
  handleRegister: (email: string, password: string) => boolean;
  handleLogin: (email: string, password: string) => void;
  handleForgotPassword: (email: string) => boolean;
  authError: string | null;
  setAuthError: (error: string | null) => void;
}

type AuthView = 'login' | 'register' | 'forgotPassword';

const AuthPage: React.FC<AuthPageProps> = ({ setView, handleRegister, handleLogin, handleForgotPassword, authError, setAuthError }) => {
    const [authView, setAuthView] = useState<AuthView>('login');

    const switchView = (target: AuthView) => {
        setAuthError(null);
        setAuthView(target);
    }

    const renderAuthView = () => {
        switch (authView) {
            case 'register':
                return (
                    <Registration 
                        onSwitchToLogin={() => switchView('login')}
                        handleRegister={handleRegister}
                        authError={authError}
                    />
                );
            case 'forgotPassword':
                return (
                    <ForgotPassword
                        onSwitchToLogin={() => switchView('login')}
                        handleForgotPassword={handleForgotPassword}
                        authError={authError}
                    />
                );
            case 'login':
            default:
                 return (
                    <Login 
                        onSwitchToRegister={() => switchView('register')}
                        onSwitchToForgotPassword={() => switchView('forgotPassword')} 
                        handleLogin={handleLogin}
                        authError={authError}
                    />
                );
        }
    }

    return (
        <section className="py-20 bg-zinc-50 dark:bg-zinc-900 min-h-[calc(100vh-144px)] flex items-center">
            <div className="container mx-auto px-4 flex flex-col items-center">
                 <button onClick={() => setView('home')} className="flex-shrink-0 flex items-center group mb-6 cursor-pointer">
                    <LegalIcon className="h-10 w-10 text-brand-600" />
                    <span className="ml-3 text-3xl font-bold text-zinc-900 dark:text-white">LexiGen</span>
                 </button>

                <div className="w-full max-w-md bg-white dark:bg-zinc-950 p-8 md:p-10 rounded-2xl shadow-lg border border-zinc-200 dark:border-zinc-800">
                    {renderAuthView()}
                </div>
            </div>
        </section>
    );
};

export default AuthPage;