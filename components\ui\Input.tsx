import React, { useState } from 'react';
import { cn } from '../../lib/utils';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  helperText?: string;
  errorText?: string;
  successText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerClassName?: string;
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'sm' | 'default' | 'lg';
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      helperText,
      errorText,
      successText,
      leftIcon,
      rightIcon,
      className,
      containerClassName,
      variant = 'default',
      size = 'default',
      id,
      onFocus,
      onBlur,
      ...props
    },
    ref
  ) => {
    const [isFocused, setIsFocused] = useState(false);
    const inputId = id || React.useId();
    const hasError = !!errorText;
    const hasSuccess = !!successText && !hasError;

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      onBlur?.(e);
    };

    const baseClasses = 'block w-full transition-all duration-200 ease-out focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed';

    const variantClasses = {
      default: cn(
        'bg-white dark:bg-neutral-900 border border-neutral-300 dark:border-neutral-700',
        'focus:border-brand-500 focus:ring-2 focus:ring-brand-500/20',
        hasError && 'border-error-500 focus:border-error-500 focus:ring-error-500/20',
        hasSuccess && 'border-success-500 focus:border-success-500 focus:ring-success-500/20'
      ),
      filled: cn(
        'bg-neutral-100 dark:bg-neutral-800 border border-transparent',
        'focus:bg-white dark:focus:bg-neutral-900 focus:border-brand-500 focus:ring-2 focus:ring-brand-500/20',
        hasError && 'bg-error-50 dark:bg-error-900/20 border-error-500 focus:border-error-500 focus:ring-error-500/20',
        hasSuccess && 'bg-success-50 dark:bg-success-900/20 border-success-500 focus:border-success-500 focus:ring-success-500/20'
      ),
      outlined: cn(
        'bg-transparent border-2 border-neutral-300 dark:border-neutral-700',
        'focus:border-brand-500 focus:ring-2 focus:ring-brand-500/20',
        hasError && 'border-error-500 focus:border-error-500 focus:ring-error-500/20',
        hasSuccess && 'border-success-500 focus:border-success-500 focus:ring-success-500/20'
      ),
    };

    const sizeClasses = {
      sm: 'h-8 text-sm rounded-lg',
      default: 'h-10 text-sm rounded-xl',
      lg: 'h-12 text-base rounded-xl',
    };

    const paddingClasses = cn(
      leftIcon && rightIcon ? 'px-10' : leftIcon ? 'pl-10 pr-4' : rightIcon ? 'pl-4 pr-10' : 'px-4'
    );

    return (
      <div className={cn('w-full', containerClassName)}>
        {label && (
          <label
            htmlFor={inputId}
            className={cn(
              'block text-sm font-medium mb-2 transition-colors',
              hasError ? 'text-error-700 dark:text-error-400' :
                hasSuccess ? 'text-success-700 dark:text-success-400' :
                  isFocused ? 'text-brand-700 dark:text-brand-400' :
                    'text-neutral-700 dark:text-neutral-300'
            )}
          >
            {label}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <span className={cn(
              'absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none transition-colors',
              hasError ? 'text-error-500' :
                hasSuccess ? 'text-success-500' :
                  isFocused ? 'text-brand-500' :
                    'text-neutral-400 dark:text-neutral-500'
            )}>
              {leftIcon}
            </span>
          )}
          <input
            id={inputId}
            ref={ref}
            className={cn(
              baseClasses,
              variantClasses[variant],
              sizeClasses[size],
              paddingClasses,
              'text-neutral-900 dark:text-neutral-100',
              'placeholder-neutral-400 dark:placeholder-neutral-500',
              className
            )}
            onFocus={handleFocus}
            onBlur={handleBlur}
            {...props}
          />
          {rightIcon && (
            <span className={cn(
              'absolute inset-y-0 right-0 pr-3 flex items-center transition-colors',
              hasError ? 'text-error-500' :
                hasSuccess ? 'text-success-500' :
                  isFocused ? 'text-brand-500' :
                    'text-neutral-400 dark:text-neutral-500'
            )}>
              {rightIcon}
            </span>
          )}
        </div>
        {hasError ? (
          <p className="mt-2 text-xs text-error-600 dark:text-error-400 flex items-center gap-1">
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {errorText}
          </p>
        ) : hasSuccess ? (
          <p className="mt-2 text-xs text-success-600 dark:text-success-400 flex items-center gap-1">
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            {successText}
          </p>
        ) : helperText ? (
          <p className="mt-2 text-xs text-neutral-500 dark:text-neutral-400">{helperText}</p>
        ) : null}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
