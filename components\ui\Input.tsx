import React from 'react';
import { cn } from '../../lib/utils';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  helperText?: string;
  errorText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerClassName?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    { label, helperText, errorText, leftIcon, rightIcon, className, containerClassName, id, ...props },
    ref
  ) => {
    const inputId = id || React.useId();
    const hasError = !!errorText;
    return (
      <div className={cn('w-full', containerClassName)}>
        {label && (
          <label htmlFor={inputId} className="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
            {label}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <span className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-zinc-400">
              {leftIcon}
            </span>
          )}
          <input
            id={inputId}
            ref={ref}
            className={cn(
              'block w-full rounded-md border bg-white dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100',
              'placeholder-zinc-400 dark:placeholder-zinc-500',
              'focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500',
              'disabled:opacity-60 disabled:cursor-not-allowed',
              leftIcon ? 'pl-10 pr-3 py-2 border-zinc-300 dark:border-zinc-700' : 'px-3 py-2 border-zinc-300 dark:border-zinc-700',
              rightIcon ? 'pr-10' : '',
              hasError ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : '',
              className
            )}
            {...props}
          />
          {rightIcon && (
            <span className="absolute inset-y-0 right-0 pr-3 flex items-center text-zinc-400">
              {rightIcon}
            </span>
          )}
        </div>
        {hasError ? (
          <p className="mt-1 text-xs text-red-600">{errorText}</p>
        ) : helperText ? (
          <p className="mt-1 text-xs text-zinc-500 dark:text-zinc-400">{helperText}</p>
        ) : null}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;

